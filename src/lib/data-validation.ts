// 数据验证工具

import { DataValue, DataObject, DataRule, ValidationResult, ValidationError, ValidationWarning } from '@/api/data-management/types';

// 验证数据值
export function validateDataValue(value: DataValue, path: string[] = []): ValidationResult {
  const errors: ValidationError[] = [];
  const warnings: ValidationWarning[] = [];

  try {
    // 递归验证对象
    if (typeof value === 'object' && value !== null) {
      if ('type' in value && value.type === 'rule') {
        // 验证规则
        const ruleValidation = validateRule(value as DataRule, path);
        errors.push(...ruleValidation.errors);
        warnings.push(...ruleValidation.warnings);
      } else {
        // 验证普通对象
        const objValidation = validateObject(value as DataObject, path);
        errors.push(...objValidation.errors);
        warnings.push(...objValidation.warnings);
      }
    }

    // 验证字符串
    if (typeof value === 'string') {
      const stringValidation = validateString(value, path);
      errors.push(...stringValidation.errors);
      warnings.push(...stringValidation.warnings);
    }

    // 验证数字
    if (typeof value === 'number') {
      const numberValidation = validateNumber(value, path);
      errors.push(...numberValidation.errors);
      warnings.push(...numberValidation.warnings);
    }

  } catch (error) {
    errors.push({
      path,
      message: `验证过程中发生错误: ${error}`,
      code: 'VALIDATION_ERROR'
    });
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

// 验证对象
function validateObject(obj: DataObject, path: string[]): ValidationResult {
  const errors: ValidationError[] = [];
  const warnings: ValidationWarning[] = [];

  // 检查对象是否为空
  if (Object.keys(obj).length === 0) {
    warnings.push({
      path,
      message: '对象为空',
      suggestion: '考虑添加一些属性或删除此对象'
    });
  }

  // 检查键名规范
  Object.keys(obj).forEach(key => {
    // 检查键名是否包含特殊字符
    if (!/^[a-zA-Z0-9_-]+$/.test(key)) {
      warnings.push({
        path: [...path, key],
        message: '键名包含特殊字符',
        suggestion: '建议使用字母、数字、下划线或连字符'
      });
    }

    // 检查键名长度
    if (key.length > 50) {
      warnings.push({
        path: [...path, key],
        message: '键名过长',
        suggestion: '建议键名长度不超过50个字符'
      });
    }

    // 递归验证子值
    const childValidation = validateDataValue(obj[key], [...path, key]);
    errors.push(...childValidation.errors);
    warnings.push(...childValidation.warnings);
  });

  return { isValid: errors.length === 0, errors, warnings };
}

// 验证规则
function validateRule(rule: DataRule, path: string[]): ValidationResult {
  const errors: ValidationError[] = [];
  const warnings: ValidationWarning[] = [];

  // 验证规则ID
  if (!rule.ruleId || rule.ruleId.trim() === '') {
    errors.push({
      path,
      message: '规则ID不能为空',
      code: 'RULE_ID_REQUIRED'
    });
  } else if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(rule.ruleId)) {
    errors.push({
      path,
      message: '规则ID格式不正确',
      code: 'RULE_ID_INVALID'
    });
  }

  // 验证规则描述
  if (!rule.description || rule.description.trim() === '') {
    warnings.push({
      path,
      message: '规则描述为空',
      suggestion: '建议添加规则描述以便理解'
    });
  }

  // 验证规则条件
  if (!rule.conditions || rule.conditions.length === 0) {
    errors.push({
      path,
      message: '规则必须包含至少一个条件',
      code: 'RULE_CONDITIONS_REQUIRED'
    });
  } else {
    rule.conditions.forEach((condition, index) => {
      // 验证条件表达式
      if (!condition.condition || condition.condition.trim() === '') {
        errors.push({
          path: [...path, 'conditions', index.toString()],
          message: '条件表达式不能为空',
          code: 'CONDITION_EXPRESSION_REQUIRED'
        });
      } else {
        // 简单的语法检查
        try {
          // 检查是否包含基本的比较操作符
          const hasComparison = /[=<>!]+/.test(condition.condition);
          if (!hasComparison) {
            warnings.push({
              path: [...path, 'conditions', index.toString()],
              message: '条件表达式可能缺少比较操作符',
              suggestion: '确保条件表达式包含适当的比较操作符'
            });
          }
        } catch (error) {
          warnings.push({
            path: [...path, 'conditions', index.toString()],
            message: '条件表达式语法可能有问题',
            suggestion: '请检查条件表达式的语法'
          });
        }
      }

      // 验证返回值
      if (condition.value === undefined) {
        warnings.push({
          path: [...path, 'conditions', index.toString()],
          message: '条件返回值未定义',
          suggestion: '建议明确指定条件的返回值'
        });
      }
    });
  }

  return { isValid: errors.length === 0, errors, warnings };
}

// 验证字符串
function validateString(str: string, path: string[]): ValidationResult {
  const errors: ValidationError[] = [];
  const warnings: ValidationWarning[] = [];

  // 检查字符串长度
  if (str.length > 1000) {
    warnings.push({
      path,
      message: '字符串过长',
      suggestion: '考虑缩短字符串或使用引用'
    });
  }

  // 检查是否包含可能的SQL注入
  const sqlPatterns = /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER)\b)/i;
  if (sqlPatterns.test(str)) {
    warnings.push({
      path,
      message: '字符串包含SQL关键字',
      suggestion: '如果这不是预期的，请检查内容'
    });
  }

  // 检查是否包含脚本标签
  const scriptPatterns = /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi;
  if (scriptPatterns.test(str)) {
    errors.push({
      path,
      message: '字符串包含脚本标签',
      code: 'SCRIPT_TAG_DETECTED'
    });
  }

  return { isValid: errors.length === 0, errors, warnings };
}

// 验证数字
function validateNumber(num: number, path: string[]): ValidationResult {
  const errors: ValidationError[] = [];
  const warnings: ValidationWarning[] = [];

  // 检查是否为有效数字
  if (!isFinite(num)) {
    errors.push({
      path,
      message: '数字不是有限值',
      code: 'NUMBER_NOT_FINITE'
    });
  }

  // 检查是否为NaN
  if (isNaN(num)) {
    errors.push({
      path,
      message: '值不是有效数字',
      code: 'NUMBER_NAN'
    });
  }

  // 检查数字范围（根据业务需求调整）
  if (Math.abs(num) > 1e10) {
    warnings.push({
      path,
      message: '数字值过大',
      suggestion: '考虑使用更合理的数值范围'
    });
  }

  return { isValid: errors.length === 0, errors, warnings };
}

// 验证路径
export function validatePath(path: string[]): ValidationResult {
  const errors: ValidationError[] = [];
  const warnings: ValidationWarning[] = [];

  // 检查路径深度
  if (path.length > 10) {
    warnings.push({
      path,
      message: '路径嵌套过深',
      suggestion: '考虑重构数据结构以减少嵌套层级'
    });
  }

  // 检查路径中的键名
  path.forEach((key, index) => {
    if (typeof key !== 'string' || key.trim() === '') {
      errors.push({
        path: path.slice(0, index + 1),
        message: '路径中包含无效的键名',
        code: 'INVALID_PATH_KEY'
      });
    }
  });

  return { isValid: errors.length === 0, errors, warnings };
}

// 验证数据修改操作
export function validateDataModification(
  operation: 'create' | 'update' | 'delete',
  path: string[],
  value?: DataValue,
  existingData?: any
): ValidationResult {
  const errors: ValidationError[] = [];
  const warnings: ValidationWarning[] = [];

  // 验证路径
  const pathValidation = validatePath(path);
  errors.push(...pathValidation.errors);
  warnings.push(...pathValidation.warnings);

  // 根据操作类型进行验证
  switch (operation) {
    case 'create':
      if (value === undefined) {
        errors.push({
          path,
          message: '创建操作需要提供值',
          code: 'CREATE_VALUE_REQUIRED'
        });
      } else {
        const valueValidation = validateDataValue(value, path);
        errors.push(...valueValidation.errors);
        warnings.push(...valueValidation.warnings);
      }
      break;

    case 'update':
      if (value === undefined) {
        errors.push({
          path,
          message: '更新操作需要提供新值',
          code: 'UPDATE_VALUE_REQUIRED'
        });
      } else {
        const valueValidation = validateDataValue(value, path);
        errors.push(...valueValidation.errors);
        warnings.push(...valueValidation.warnings);
      }
      break;

    case 'delete':
      if (path.length === 0) {
        errors.push({
          path,
          message: '不能删除根节点',
          code: 'DELETE_ROOT_NOT_ALLOWED'
        });
      }
      break;
  }

  return { isValid: errors.length === 0, errors, warnings };
}
