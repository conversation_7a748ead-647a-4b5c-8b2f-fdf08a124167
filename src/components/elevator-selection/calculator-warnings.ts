import { ElevatorSelectionData } from "@/lib/elevator-selection";

// 警告类型定义
export interface Warning {
  id: string;
  type: 'error' | 'warning' | 'info';
  title: string;
  message: string;
  formula?: string;
  parameters?: { [key: string]: any };
  section: string;
}

// 警告计算结果
export interface WarningResult {
  warnings: Warning[];
  errorCount: number;
  warningCount: number;
  infoCount: number;
}

// 电梯型号配置数据
const ELEVATOR_CONFIG = {
  LTHX: { minCapacity: 1600, maxCapacity: 5000 },
  LTHW: { minCapacity: 1600, maxCapacity: 5000 },
  EVIK: { minCapacity: 320, maxCapacity: 2000 },
  EVIN: { minCapacity: 320, maxCapacity: 2000 },
  "LTHX Car": { minCapacity: 3000, maxCapacity: 5000 },
  "LTHW Car": { minCapacity: 3000, maxCapacity: 5000 },
};

// 轿厢最小尺寸配置
const CAR_MIN_SIZES = {
  LTHX: { minWidth: 1100, minDepth: 1100 },
  LTHW: { minWidth: 1100, minDepth: 1100 },
  EVIK: { minWidth: 1100, minDepth: 1100 },
  EVIN: { minWidth: 1100, minDepth: 1100 },
  "LTHX Car": { minWidth: 1100, minDepth: 1100 },
  "LTHW Car": { minWidth: 1100, minDepth: 1100 },
};

// 门开启配置
const DOOR_LIMITS = {
  C2: { min: 600, max: 1300 },
  S2: { min: 600, max: 2000 },
  C4: { min: 1300, max: 3000 },
  S3: { min: 800, max: 2000 },
};

export function calculateWarnings(data: ElevatorSelectionData): WarningResult {
  const warnings: Warning[] = [];

  // 3.1 后对重警告
  const rearCwtWarnings = calculateRearCwtWarnings(data);
  warnings.push(...rearCwtWarnings);

  // 3.2-3.5 井道尺寸警告
  const shaftWarnings = calculateShaftWarnings(data);
  warnings.push(...shaftWarnings);

  // 3.6 载重警告
  const capacityWarnings = calculateCapacityWarnings(data);
  warnings.push(...capacityWarnings);

  // 3.7 乘客人数警告
  const passengerWarnings = calculatePassengerWarnings(data);
  warnings.push(...passengerWarnings);

  // 3.10 载重标准警告
  const standardCapacityWarnings = calculateStandardCapacityWarnings(data);
  warnings.push(...standardCapacityWarnings);

  // 3.11 轿厢大小警告
  const carSizeWarnings = calculateCarSizeWarnings(data);
  warnings.push(...carSizeWarnings);

  // 3.12 轿厢高度与开门高度差值警告
  const carHeightWarnings = calculateCarHeightWarnings(data);
  warnings.push(...carHeightWarnings);

  // 3.13 运行高度警告
  const travelHeightWarnings = calculateTravelHeightWarnings(data);
  warnings.push(...travelHeightWarnings);

  // 3.14 门宽+后对重警告
  const doorWarnings = calculateDoorWarnings(data);
  warnings.push(...doorWarnings);

  // 统计警告数量
  const errorCount = warnings.filter(w => w.type === 'error').length;
  const warningCount = warnings.filter(w => w.type === 'warning').length;
  const infoCount = warnings.filter(w => w.type === 'info').length;

  return {
    warnings,
    errorCount,
    warningCount,
    infoCount,
  };
}

// 3.1 后对重警告
function calculateRearCwtWarnings(data: ElevatorSelectionData): Warning[] {
  const warnings: Warning[] = [];

  // 3.1.1 不适用对重后置
  const isIncompatibleRearCwt = 
    (['LTHX', 'LTHW', 'EVIN'].includes(data.liftModel) ||
     (['EVIK', 'LTK'].includes(data.liftModel) && data.capacity > 1600) ||
     (data.liftModel === 'EVIK' && data.standard === 'GOST 33984.1' && data.capacity === 630)) &&
    data.cwtPosition === 'REAR';

  if (isIncompatibleRearCwt) {
    warnings.push({
      id: 'rear-cwt-incompatible',
      type: 'error',
      title: '对重位置不兼容',
      message: '此梯型规格不适用后对重',
      formula: `if ((Lift_Model in ['LTHX', 'LTHW', 'EVIN']) or 
                    (Lift_Model in ['EVIK', 'LTK'] and Capacity > 1600) or 
                    (Lift_Model == "EVIK" and Standard == "GOST 33984.1" and Capacity == 630)) 
                    and CWT_Position == "REAR"`,
      parameters: {
        liftModel: data.liftModel,
        capacity: data.capacity,
        cwtPosition: data.cwtPosition,
        standard: data.standard,
      },
      section: '3.1.1',
    });
  }

  // 3.1.2 对重后置贯通门
  if (data.cwtPosition === 'REAR' && data.throughDoor) {
    warnings.push({
      id: 'rear-cwt-through-door',
      type: 'warning',
      title: '对重后置贯通门',
      message: '后置对重与贯通门组合需要特别注意',
      formula: 'if CWT_Position == "REAR" and Through_Door == "Yes"',
      parameters: {
        cwtPosition: data.cwtPosition,
        throughDoor: data.throughDoor,
      },
      section: '3.1.2',
    });
  }

  return warnings;
}

// 3.2-3.5 井道尺寸警告
function calculateShaftWarnings(data: ElevatorSelectionData): Warning[] {
  const warnings: Warning[] = [];

  // 假设的最小井道尺寸计算（实际应根据具体计算公式）
  const minShaftWidth = data.carWidth + 200; // 简化计算
  const minShaftDepth = data.carDepth + 200; // 简化计算
  const minOverhead = 3500; // 简化值
  const minPitDepth = 1200; // 简化值

  // // 3.2 井道宽度警告
  // if (data.shaftWidth && data.shaftWidth < minShaftWidth) {
  //   warnings.push({
  //     id: 'shaft-width-too-small',
  //     type: 'error',
  //     title: '井道宽度过小',
  //     message: 'Wrong! to increase SW, adjust CW or Door',
  //     formula: 'Shaft_Width < 最小井道宽度',
  //     parameters: {
  //       shaftWidth: data.shaftWidth,
  //       minShaftWidth,
  //       carWidth: data.carWidth,
  //       doorWidth: data.doorWidth,
  //     },
  //     section: '3.2',
  //   });
  // } else if (data.shaftWidth && data.shaftWidth > minShaftWidth + 1000) {
  //   warnings.push({
  //     id: 'shaft-width-too-large',
  //     type: 'warning',
  //     title: '井道宽度过大',
  //     message: 'Separate beam is needed',
  //     formula: 'Shaft_Width > 最小井道宽度 + 1000',
  //     parameters: {
  //       shaftWidth: data.shaftWidth,
  //       minShaftWidth,
  //       threshold: minShaftWidth + 1000,
  //     },
  //     section: '3.2',
  //   });
  // }

  // 3.3 井道深度警告
  if (data.shaftDepth && data.shaftDepth < minShaftDepth) {
    warnings.push({
      id: 'shaft-depth-too-small',
      type: 'error',
      title: '井道深度过小',
      message: 'Wrong! to increase SD, adjust CD',
      formula: 'Shaft_Depth < 最小井道深度',
      parameters: {
        shaftDepth: data.shaftDepth,
        minShaftDepth,
        carDepth: data.carDepth,
      },
      section: '3.3',
    });
  } else if (data.cwtPosition === 'REAR' && data.shaftDepth && data.shaftDepth > minShaftDepth + 1000) {
    warnings.push({
      id: 'shaft-depth-too-large',
      type: 'warning',
      title: '井道深度过大',
      message: 'Separate beam may needed',
      formula: 'CWT Position =="REAR" and Shaft_Depth > 最小井道深度 + 1000',
      parameters: {
        shaftDepth: data.shaftDepth,
        minShaftDepth,
        cwtPosition: data.cwtPosition,
      },
      section: '3.3',
    });
  }

  // 3.4 顶层高度警告
  if (data.overhead && data.overhead < minOverhead) {
    warnings.push({
      id: 'overhead-too-small',
      type: 'error',
      title: '顶层高度过小',
      message: 'Wrong! to increase K, reduce CH or V',
      formula: 'Overhead < 最小顶层高度',
      parameters: {
        overhead: data.overhead,
        minOverhead,
        carHeight: data.carHeight,
      },
      section: '3.4',
    });
  } else if (['LTHW', 'LTW'].includes(data.liftModel) && data.overhead && data.overhead > minOverhead + 2000) {
    warnings.push({
      id: 'overhead-too-large',
      type: 'warning',
      title: '顶层高度过大',
      message: 'Beam to hang is needed at shaft top',
      formula: 'Lift_Model in ["LTHW", "LTW"] and Overhead > 最小顶层高度 + 2000',
      parameters: {
        overhead: data.overhead,
        minOverhead,
        liftModel: data.liftModel,
      },
      section: '3.4',
    });
  }

  // 3.5 底坑深度警告
  if (data.pitDepth && data.pitDepth < minPitDepth) {
    warnings.push({
      id: 'pit-depth-too-small',
      type: 'error',
      title: '底坑深度过小',
      message: 'Wrong! to increase pit or reduce speed',
      formula: 'Pit_Depth < 最小地坑深度',
      parameters: {
        pitDepth: data.pitDepth,
        minPitDepth,
        speed: data.speed,
      },
      section: '3.5',
    });
  } else if (data.pitDepth && data.pitDepth > 2500) {
    warnings.push({
      id: 'pit-depth-too-large',
      type: 'warning',
      title: '底坑深度过大',
      message: 'Need pit access door, please reduce if possible',
      formula: 'Pit_Depth > 2500',
      parameters: {
        pitDepth: data.pitDepth,
        threshold: 2500,
      },
      section: '3.5',
    });
  }

  return warnings;
}

// 3.6 载重警告
function calculateCapacityWarnings(data: ElevatorSelectionData): Warning[] {
  const warnings: Warning[] = [];

  // 计算轿厢面积对应载重（简化计算）
  const carArea = (data.carWidth * data.carDepth) / 1000000; // 转换为平方米
  const areaCapacity = Math.floor(carArea * 545); // 简化：400kg/m²

  if (areaCapacity > data.capacity) {
    warnings.push({
      id: 'area-capacity-mismatch',
      type: 'info',
      title: '载重面积提示',
      message: `轿厢面积对应载重${areaCapacity}kg`,
      formula: 'Area_Cap = (Car_Width × Car_Depth) / 1000000 × 400',
      parameters: {
        carWidth: data.carWidth,
        carDepth: data.carDepth,
        carArea: carArea.toFixed(2),
        areaCapacity,
        selectedCapacity: data.capacity,
      },
      section: '3.6',
    });
  }

  return warnings;
}

// 3.7 乘客人数警告
function calculatePassengerWarnings(data: ElevatorSelectionData): Warning[] {
  const warnings: Warning[] = [];

  const calculatedPersons = Math.floor(data.capacity / 75);
  const standardPersons = Math.floor(data.capacity / 80); // 标准计算

  if (standardPersons < calculatedPersons && !['LTHX', 'LTHX Car', 'LTHW', 'LTHW Car'].includes(data.liftModel)) {
    warnings.push({
      id: 'passenger-count-warning',
      type: 'warning',
      title: '乘客人数建议',
      message: `乘客人数${calculatedPersons}减小为${standardPersons}，建议增大轿厢尺寸！`,
      formula: 'NPS查 < 载重计算乘客人数',
      parameters: {
        capacity: data.capacity,
        calculatedPersons,
        standardPersons,
        liftModel: data.liftModel,
      },
      section: '3.7',
    });
  }

  return warnings;
}

// 3.10 载重标准警告
function calculateStandardCapacityWarnings(data: ElevatorSelectionData): Warning[] {
  const warnings: Warning[] = [];

  const config = ELEVATOR_CONFIG[data.liftModel as keyof typeof ELEVATOR_CONFIG];
  if (!config) return warnings;

  const carArea = (data.carWidth * data.carDepth) / 1000000;
  const areaCapacity = Math.floor(carArea * 400);

  // 检查是否小于最小载重
  if (!['LTHX', 'LTHW', 'LTHX Car', 'LTHW Car'].includes(data.liftModel) && areaCapacity < config.minCapacity) {
    warnings.push({
      id: 'capacity-below-min',
      type: 'warning',
      title: '载重过小',
      message: `建议加大轿厢尺寸，当前已小于标配载重最小值 ${config.minCapacity}kg`,
      parameters: {
        areaCapacity,
        minCapacity: config.minCapacity,
        liftModel: data.liftModel,
      },
      section: '3.10',
    });
  }

  // 检查是否超过最大载重
  if (areaCapacity > config.maxCapacity) {
    warnings.push({
      id: 'capacity-above-max',
      type: 'warning',
      title: '载重过大',
      message: `建议减小轿厢尺寸，当前已超出标配载重最大值 ${config.maxCapacity}kg`,
      parameters: {
        areaCapacity,
        maxCapacity: config.maxCapacity,
        liftModel: data.liftModel,
      },
      section: '3.10',
    });
  }

  // 轿厢面积超标检查
  if ((data.floorExceedCode && areaCapacity > data.capacity) || (!data.floorExceedCode && areaCapacity > data.capacity)) {
    warnings.push({
      id: 'car-area-exceed',
      type: 'error',
      title: '轿厢面积超标',
      message: '轿厢面积超标，请减小轿厢尺寸！',
      parameters: {
        areaCapacity,
        selectedCapacity: data.capacity,
        floorExceedCode: data.floorExceedCode,
      },
      section: '3.10',
    });
  }

  return warnings;
}

// 3.11 轿厢大小警告
function calculateCarSizeWarnings(data: ElevatorSelectionData): Warning[] {
  const warnings: Warning[] = [];

  const minSizes = CAR_MIN_SIZES[data.liftModel as keyof typeof CAR_MIN_SIZES];
  if (!minSizes) return warnings;

  // 轿厢宽度检查
  if (data.carWidth < minSizes.minWidth) {
    warnings.push({
      id: 'car-width-too-small',
      type: 'warning',
      title: '轿厢宽度过小',
      message: '请增大轿厢宽度！',
      parameters: {
        carWidth: data.carWidth,
        minWidth: minSizes.minWidth,
        liftModel: data.liftModel,
      },
      section: '3.11.1',
    });
  }

  // 轿厢深度检查
  if (data.carDepth < minSizes.minDepth) {
    warnings.push({
      id: 'car-depth-too-small',
      type: 'warning',
      title: '轿厢深度过小',
      message: '请增大轿厢深度！',
      parameters: {
        carDepth: data.carDepth,
        minDepth: minSizes.minDepth,
        liftModel: data.liftModel,
      },
      section: '3.11.2',
    });
  }

  // 开门高度检查
  if (data.doorHeight < 2000) {
    warnings.push({
      id: 'door-height-too-small',
      type: 'warning',
      title: '开门高度过小',
      message: '请增大开门高度！',
      parameters: {
        doorHeight: data.doorHeight,
        minDoorHeight: 2000,
      },
      section: '3.11.3',
    });
  }

  return warnings;
}

// 3.12 轿厢高度与开门高度差值警告
function calculateCarHeightWarnings(data: ElevatorSelectionData): Warning[] {
  const warnings: Warning[] = [];

  const heightDiff = data.carHeight - data.doorHeight;

  if (['LTHX', 'LTHX Car', 'LTHW', 'LTHW Car'].includes(data.liftModel)) {
    if (heightDiff < 100) {
      warnings.push({
        id: 'car-door-height-diff-small',
        type: 'warning',
        title: '轿厢与门高度差过小',
        message: '请增大轿厢高或减小开门高!',
        formula: '(Car_Height - Door_Height) < 100',
        parameters: {
          carHeight: data.carHeight,
          doorHeight: data.doorHeight,
          heightDiff,
          liftModel: data.liftModel,
        },
        section: '3.12',
      });
    } else if (heightDiff > 300) {
      warnings.push({
        id: 'car-door-height-diff-large',
        type: 'warning',
        title: '轿厢与门高度差过大',
        message: '请减小轿厢高或增大开门高!',
        formula: '(Car_Height - Door_Height) > 300',
        parameters: {
          carHeight: data.carHeight,
          doorHeight: data.doorHeight,
          heightDiff,
          liftModel: data.liftModel,
        },
        section: '3.12',
      });
    }
  } else {
    if (heightDiff < 50) {
      warnings.push({
        id: 'car-door-height-diff-small-standard',
        type: 'warning',
        title: '轿厢与门高度差过小',
        message: '请增大轿厢高或减小开门高!',
        formula: '(Car_Height - Door_Height) < 50',
        parameters: {
          carHeight: data.carHeight,
          doorHeight: data.doorHeight,
          heightDiff,
          liftModel: data.liftModel,
        },
        section: '3.12',
      });
    }
  }

  return warnings;
}

// 3.13 运行高度警告
function calculateTravelHeightWarnings(data: ElevatorSelectionData): Warning[] {
  const warnings: Warning[] = [];

  // 标准运行高度（简化计算）
  const standardTravelHeight = 50; // 简化值，实际应根据型号和速度查表

  if (data.travelHeight > standardTravelHeight) {
    warnings.push({
      id: 'travel-height-exceed',
      type: 'warning',
      title: '运行高度超标',
      message: `超出标配运行高度最大值 ${standardTravelHeight}m`,
      formula: 'Travel_Height > TH0',
      parameters: {
        travelHeight: data.travelHeight,
        standardTravelHeight,
        liftModel: data.liftModel,
        speed: data.speed,
      },
      section: '3.13',
    });
  }

  return warnings;
}

// 3.14 门宽+后对重警告
function calculateDoorWarnings(data: ElevatorSelectionData): Warning[] {
  const warnings: Warning[] = [];

  // 门宽检查
  const doorOverhang = Math.abs((data.carWidth - data.doorWidth) / 2);
  if (doorOverhang > data.carWidth / 2) {
    warnings.push({
      id: 'door-width-exceed-car',
      type: 'error',
      title: '门宽超出轿厢范围',
      message: '门宽超出轿厢范围，请减小',
      formula: 'abs(De) > ((Car_Width - Door_Width) / 2)',
      parameters: {
        carWidth: data.carWidth,
        doorWidth: data.doorWidth,
        doorOverhang,
      },
      section: '3.14',
    });
  }

  // 门宽范围检查
  const doorLimits = DOOR_LIMITS[data.doorOpening as keyof typeof DOOR_LIMITS];
  if (doorLimits && (data.doorWidth < doorLimits.min || data.doorWidth > doorLimits.max)) {
    warnings.push({
      id: 'door-width-out-of-range',
      type: 'warning',
      title: '门宽范围异常',
      message: '门宽请询售前',
      parameters: {
        doorOpening: data.doorOpening,
        doorWidth: data.doorWidth,
        allowedRange: doorLimits,
      },
      section: '3.14',
    });
  }

  return warnings;
} 