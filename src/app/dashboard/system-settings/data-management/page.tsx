"use client";

import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  IconDatabase, 
  IconSearch, 
  IconDownload, 
  IconUpload, 
  IconSettings,
  IconEye,
  IconEdit,
  IconTrash,
  IconPlus,
  IconRefresh
} from '@tabler/icons-react';
import { toast } from 'sonner';
import { DataModule } from '@/api/data-management/types';
import { getDataModules } from '@/api/data-management/api';
import Link from 'next/link';
import { Loader2 } from 'lucide-react';
import { AppleStyleCardLoading } from '@/components/data-management/AppleStyleLoading';

// 分类颜色映射
const categoryColors = {
  basic: 'bg-blue-100 text-blue-800 border-blue-200',
  dimension: 'bg-green-100 text-green-800 border-green-200',
  safety: 'bg-red-100 text-red-800 border-red-200',
  door: 'bg-purple-100 text-purple-800 border-purple-200',
  capacity: 'bg-orange-100 text-orange-800 border-orange-200',
};

// 数据类型图标映射
const dataTypeIcons = {
  simple: '📊',
  nested: '🗂️',
  rules: '⚙️',
  mixed: '🔀',
};

export default function DataManagementPage() {
  const [modules, setModules] = useState<DataModule[]>([]);
  const [filteredModules, setFilteredModules] = useState<DataModule[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // 加载数据模块
  const loadModules = async () => {
    try {
      setIsLoading(true);
      const response = await getDataModules();
      if (response.code === 0) {
        setModules(response.data);
        setFilteredModules(response.data);
      } else {
        toast.error('加载数据模块失败');
      }
    } catch (error) {
      console.error('加载数据模块失败:', error);
      toast.error('加载数据模块失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 过滤模块
  const filterModules = () => {
    let filtered = modules;

    // 按搜索词过滤
    if (searchTerm) {
      filtered = filtered.filter(module =>
        module.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        module.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        module.fileName.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // 按分类过滤
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(module => module.category === selectedCategory);
    }

    setFilteredModules(filtered);
  };

  // 获取分类统计
  const getCategoryStats = () => {
    const stats = modules.reduce((acc, module) => {
      acc[module.category] = (acc[module.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    return stats;
  };

  useEffect(() => {
    loadModules();
  }, []);

  useEffect(() => {
    filterModules();
  }, [searchTerm, selectedCategory, modules]);

  const categoryStats = getCategoryStats();
  const categories = [
    { key: 'all', label: '全部', count: modules.length },
    { key: 'basic', label: '基础参数', count: categoryStats.basic || 0 },
    { key: 'dimension', label: '尺寸参数', count: categoryStats.dimension || 0 },
    { key: 'safety', label: '安全参数', count: categoryStats.safety || 0 },
    { key: 'door', label: '门参数', count: categoryStats.door || 0 },
    { key: 'capacity', label: '载重参数', count: categoryStats.capacity || 0 },
  ];

  if (isLoading) {
    return <AppleStyleCardLoading text="加载数据模块中..." />;
  }

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">数据管理</h1>
          <p className="text-muted-foreground">
            管理电梯设计计算系统的查表模块数据
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={loadModules}>
            <IconRefresh className="h-4 w-4 mr-1" />
            刷新
          </Button>
          <Button variant="outline" size="sm">
            <IconDownload className="h-4 w-4 mr-1" />
            导出
          </Button>
          <Button variant="outline" size="sm">
            <IconUpload className="h-4 w-4 mr-1" />
            导入
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="border-0 bg-gradient-to-br from-blue-50 to-blue-100/50 hover:shadow-lg transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-blue-600 font-medium">总模块数</p>
                <p className="text-3xl font-bold text-blue-700 mt-1">{modules.length}</p>
              </div>
              <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center shadow-lg">
                <IconDatabase className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="border-0 bg-gradient-to-br from-green-50 to-green-100/50 hover:shadow-lg transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-green-600 font-medium">总记录数</p>
                <p className="text-3xl font-bold text-green-700 mt-1">
                  {modules.reduce((sum, m) => sum + m.recordCount, 0)}
                </p>
              </div>
              <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center shadow-lg">
                <IconSettings className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="border-0 bg-gradient-to-br from-orange-50 to-orange-100/50 hover:shadow-lg transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-orange-600 font-medium">最近更新</p>
                <p className="text-sm font-semibold text-orange-700 mt-1">
                  {modules.length > 0 ? new Date(Math.max(...modules.map(m => new Date(m.lastModified).getTime()))).toLocaleDateString() : '-'}
                </p>
              </div>
              <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-orange-500 to-orange-600 flex items-center justify-center shadow-lg">
                <IconEdit className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="border-0 bg-gradient-to-br from-purple-50 to-purple-100/50 hover:shadow-lg transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-purple-600 font-medium">数据类型</p>
                <p className="text-3xl font-bold text-purple-700 mt-1">
                  {new Set(modules.map(m => m.dataType)).size}
                </p>
              </div>
              <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center shadow-lg">
                <IconDatabase className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 搜索和过滤 */}
      <div className="flex flex-col sm:flex-row gap-6">
        <div className="relative flex-1">
          <IconSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          <Input
            placeholder="搜索模块名称、描述或文件名..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-12 h-12 border-gray-200 rounded-xl bg-white/50 backdrop-blur-sm focus:ring-2 focus:ring-blue-500/20 focus:border-blue-400 transition-all duration-200"
          />
        </div>
        <div className="flex gap-2 flex-wrap">
          {categories.map((category) => (
            <Button
              key={category.key}
              variant={selectedCategory === category.key ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory(category.key)}
              className={`whitespace-nowrap rounded-lg transition-all duration-200 ${
                selectedCategory === category.key
                  ? "bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-md hover:shadow-lg"
                  : "border-gray-200 hover:border-blue-300 hover:bg-blue-50"
              }`}
            >
              {category.label} ({category.count})
            </Button>
          ))}
        </div>
      </div>

      {/* 模块列表 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredModules.map((module) => (
          <Card
            key={module.id}
            className="group hover:shadow-lg hover:shadow-blue-500/10 transition-all duration-300 border-0 bg-gradient-to-br from-white to-gray-50/50 backdrop-blur-sm"
          >
            <CardHeader className="pb-4">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-lg flex items-center gap-3 group-hover:text-blue-600 transition-colors">
                    <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-lg shadow-lg">
                      {dataTypeIcons[module.dataType]}
                    </div>
                    <div>
                      <div className="font-semibold">{module.name}</div>
                      <div className="text-xs text-muted-foreground font-normal mt-1">
                        {module.fileName}
                      </div>
                    </div>
                  </CardTitle>
                  <CardDescription className="mt-3 text-sm leading-relaxed">
                    {module.description}
                  </CardDescription>
                </div>
              </div>
              <div className="flex gap-2 mt-4">
                <Badge className={`${categoryColors[module.category]} text-xs font-medium`}>
                  {module.category}
                </Badge>
                <Badge variant="outline" className="text-xs">
                  {module.recordCount} 条记录
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="text-xs text-muted-foreground mb-4 p-3 bg-gray-50/50 rounded-lg">
                <div className="flex items-center justify-between">
                  <span>最后更新</span>
                  <span className="font-medium">
                    {new Date(module.lastModified).toLocaleDateString()}
                  </span>
                </div>
              </div>
              <div className="flex gap-2">
                <Link href={`/dashboard/system-settings/data-management/${module.id}`} className="flex-1">
                  <Button
                    size="sm"
                    className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white border-0 shadow-md hover:shadow-lg transition-all duration-200"
                  >
                    <IconEye className="h-4 w-4 mr-2" />
                    查看详情
                  </Button>
                </Link>
                <Button
                  size="sm"
                  variant="outline"
                  className="border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-all duration-200"
                >
                  <IconEdit className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 空状态 */}
      {filteredModules.length === 0 && !isLoading && (
        <div className="text-center py-12">
          <IconDatabase className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">没有找到匹配的模块</h3>
          <p className="text-muted-foreground">
            尝试调整搜索条件或选择不同的分类
          </p>
        </div>
      )}
    </div>
  );
}
