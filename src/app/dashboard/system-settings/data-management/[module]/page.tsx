"use client";

import React, { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  IconArrowLeft, 
  IconEdit, 
  IconDownload, 
  IconUpload,
  IconHistory,
  IconBarChart,
  IconRefresh,
  IconSearch
} from '@tabler/icons-react';
import { toast } from 'sonner';
import { DataModule, TableData, DataValue } from '@/api/data-management/types';
import { getDataModule, getTableData, getStatistics, modifyData } from '@/api/data-management/api';
import { Loader2 } from 'lucide-react';
import { DataViewer } from '@/components/data-management/DataViewer';
import { DataStatistics } from '@/components/data-management/DataStatistics';

export default function DataModuleDetailPage() {
  const params = useParams();
  const router = useRouter();
  const moduleId = params.module as string;

  const [module, setModule] = useState<DataModule | null>(null);
  const [tableData, setTableData] = useState<TableData | null>(null);
  const [statistics, setStatistics] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('data');

  // 加载模块信息
  const loadModuleData = async () => {
    try {
      setIsLoading(true);
      
      // 并行加载模块信息和表格数据
      const [moduleInfo, tableDataResponse, statsData] = await Promise.all([
        getDataModule(moduleId),
        getTableData(moduleId),
        getStatistics(moduleId)
      ]);

      if (!moduleInfo) {
        toast.error('模块不存在');
        router.push('/dashboard/system-settings/data-management');
        return;
      }

      setModule(moduleInfo);
      
      if (tableDataResponse.code === 0) {
        setTableData(tableDataResponse.data);
      } else {
        toast.error('加载表格数据失败');
      }

      setStatistics(statsData);
    } catch (error) {
      console.error('加载模块数据失败:', error);
      toast.error('加载模块数据失败');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (moduleId) {
      loadModuleData();
    }
  }, [moduleId]);

  // 处理数据修改
  const handleDataEdit = async (path: string[], value: DataValue) => {
    try {
      const response = await modifyData({
        moduleId,
        operation: 'update',
        path,
        value,
      });

      if (response.code === 0) {
        toast.success('数据更新成功');
        // 重新加载数据
        loadModuleData();
      } else {
        toast.error('数据更新失败');
      }
    } catch (error) {
      console.error('数据更新失败:', error);
      toast.error('数据更新失败');
    }
  };

  // 处理数据删除
  const handleDataDelete = async (path: string[]) => {
    try {
      const response = await modifyData({
        moduleId,
        operation: 'delete',
        path,
      });

      if (response.code === 0) {
        toast.success('数据删除成功');
        // 重新加载数据
        loadModuleData();
      } else {
        toast.error('数据删除失败');
      }
    } catch (error) {
      console.error('数据删除失败:', error);
      toast.error('数据删除失败');
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">加载中...</span>
      </div>
    );
  }

  if (!module) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium mb-2">模块不存在</h3>
        <Button onClick={() => router.push('/dashboard/system-settings/data-management')}>
          返回数据管理
        </Button>
      </div>
    );
  }

  // 分类颜色映射
  const categoryColors = {
    basic: 'bg-blue-100 text-blue-800 border-blue-200',
    dimension: 'bg-green-100 text-green-800 border-green-200',
    safety: 'bg-red-100 text-red-800 border-red-200',
    door: 'bg-purple-100 text-purple-800 border-purple-200',
    capacity: 'bg-orange-100 text-orange-800 border-orange-200',
  };

  // 数据类型图标映射
  const dataTypeIcons = {
    simple: '📊',
    nested: '🗂️',
    rules: '⚙️',
    mixed: '🔀',
  };

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => router.push('/dashboard/system-settings/data-management')}
          >
            <IconArrowLeft className="h-4 w-4 mr-1" />
            返回
          </Button>
          <div>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <span className="text-2xl">{dataTypeIcons[module.dataType]}</span>
              {module.name}
            </h1>
            <p className="text-muted-foreground">{module.description}</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={loadModuleData}>
            <IconRefresh className="h-4 w-4 mr-1" />
            刷新
          </Button>
          <Button variant="outline" size="sm">
            <IconDownload className="h-4 w-4 mr-1" />
            导出
          </Button>
          <Button variant="outline" size="sm">
            <IconUpload className="h-4 w-4 mr-1" />
            导入
          </Button>
          <Button size="sm">
            <IconEdit className="h-4 w-4 mr-1" />
            编辑数据
          </Button>
        </div>
      </div>

      {/* 模块信息卡片 */}
      <Card>
        <CardHeader>
          <CardTitle>模块信息</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <p className="text-sm text-muted-foreground">文件名</p>
              <p className="font-medium">{module.fileName}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">分类</p>
              <Badge className={categoryColors[module.category]}>
                {module.category}
              </Badge>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">数据类型</p>
              <p className="font-medium">{module.dataType}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">记录数量</p>
              <p className="font-medium">{module.recordCount} 条</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">最后修改</p>
              <p className="font-medium">
                {new Date(module.lastModified).toLocaleString()}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 标签页内容 */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="data">数据查看</TabsTrigger>
          <TabsTrigger value="statistics">统计信息</TabsTrigger>
          <TabsTrigger value="history">变更历史</TabsTrigger>
          <TabsTrigger value="settings">设置</TabsTrigger>
        </TabsList>

        <TabsContent value="data" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>表格数据</CardTitle>
              <CardDescription>
                查看和管理 {module.name} 的数据结构
              </CardDescription>
            </CardHeader>
            <CardContent>
              {tableData ? (
                <DataViewer
                  data={tableData}
                  onEdit={handleDataEdit}
                  onDelete={handleDataDelete}
                />
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">暂无数据</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="statistics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>统计信息</CardTitle>
              <CardDescription>
                查看 {module.name} 的数据统计和分析
              </CardDescription>
            </CardHeader>
            <CardContent>
              {statistics ? (
                <DataStatistics statistics={statistics} />
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">暂无统计数据</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>变更历史</CardTitle>
              <CardDescription>
                查看 {module.name} 的数据变更记录
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <IconHistory className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">暂无变更历史</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>模块设置</CardTitle>
              <CardDescription>
                配置 {module.name} 的相关设置
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <p className="text-muted-foreground">设置功能开发中...</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
