// 数据管理API服务

import request from '@/api/request';
import {
  DataModule,
  TableData,
  DataModificationRequest,
  DataModificationResponse,
  ValidationResult,
  DataSearchRequest,
  DataSearchResult,
  DataExportRequest,
  DataImportRequest,
  DataBackup,
  DataRestoreRequest,
  DataChangeHistory,
  PaginationRequest,
  PaginatedResponse,
  DataStatistics,
  DataModuleListResponse,
  TableDataResponse,
  DataModificationApiResponse,
  ValidationResponse,
  DataSearchResponse,
} from './types';

// 模拟数据 - 查表模块列表
const mockDataModules: DataModule[] = [
  {
    id: 'C0_table_query',
    name: 'C0 门中心偏移量查询',
    description: '查询门中心相对于轿厢中心的偏移量',
    fileName: 'C0_table_query.py',
    category: 'door',
    dataType: 'mixed',
    lastModified: '2024-01-15T10:30:00Z',
    recordCount: 45,
  },
  {
    id: 'CD0_table_query',
    name: 'CD0 标准轿厢深度查询',
    description: '查询标准轿厢深度参数',
    fileName: 'CD0_table_query.py',
    category: 'dimension',
    dataType: 'nested',
    lastModified: '2024-01-14T15:20:00Z',
    recordCount: 38,
  },
  {
    id: 'CSD_table_query',
    name: 'CSD 轿厢安全距离查询',
    description: '查询轿厢安全距离相关参数',
    fileName: 'CSD_table_query.py',
    category: 'safety',
    dataType: 'rules',
    lastModified: '2024-01-13T09:15:00Z',
    recordCount: 67,
  },
  {
    id: 'CW0_table_query',
    name: 'CW0 标准轿厢宽度查询',
    description: '查询标准轿厢宽度参数',
    fileName: 'CW0_table_query.py',
    category: 'dimension',
    dataType: 'nested',
    lastModified: '2024-01-12T14:45:00Z',
    recordCount: 42,
  },
  {
    id: 'CWGB_table_query',
    name: 'CWGB 对重导轨支架查询',
    description: '查询对重导轨支架相关参数',
    fileName: 'CWGB_table_query.py',
    category: 'basic',
    dataType: 'simple',
    lastModified: '2024-01-11T11:30:00Z',
    recordCount: 28,
  },
  {
    id: 'CWG_table_query',
    name: 'CWG 对重导轨间距查询',
    description: '查询对重导轨间距参数',
    fileName: 'CWG_table_query.py',
    category: 'basic',
    dataType: 'nested',
    lastModified: '2024-01-10T16:20:00Z',
    recordCount: 35,
  },
  {
    id: 'CWe_table_query',
    name: 'CWe 对重有效宽度查询',
    description: '查询对重有效宽度参数',
    fileName: 'CWe_table_query.py',
    category: 'basic',
    dataType: 'nested',
    lastModified: '2024-01-09T13:10:00Z',
    recordCount: 31,
  },
  {
    id: 'Capacity_query',
    name: '载重范围查询',
    description: '查询各电梯型号的载重范围',
    fileName: 'Capacity_query.py',
    category: 'capacity',
    dataType: 'simple',
    lastModified: '2024-01-08T10:00:00Z',
    recordCount: 21,
  },
  {
    id: 'Car_table_query',
    name: '轿厢尺寸限制查询',
    description: '查询轿厢尺寸的最小最大限制',
    fileName: 'Car_table_query.py',
    category: 'dimension',
    dataType: 'rules',
    lastModified: '2024-01-07T12:30:00Z',
    recordCount: 56,
  },
  {
    id: 'DO0_table_query',
    name: 'DO0 标准开门类型查询',
    description: '查询标准开门类型参数',
    fileName: 'DO0_table_query.py',
    category: 'door',
    dataType: 'nested',
    lastModified: '2024-01-06T09:45:00Z',
    recordCount: 33,
  },
];

// 模拟延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// API服务类
export class DataManagementAPI {
  // 获取所有数据模块列表
  static async getDataModules(): Promise<DataModuleListResponse> {
    await delay(500); // 模拟网络延迟
    return {
      code: 0,
      msg: '获取成功',
      data: mockDataModules,
    };
  }

  // 根据ID获取数据模块
  static async getDataModule(moduleId: string): Promise<DataModule | null> {
    await delay(300);
    return mockDataModules.find(module => module.id === moduleId) || null;
  }

  // 获取表格数据
  static async getTableData(moduleId: string): Promise<TableDataResponse> {
    await delay(800);
    
    // 这里应该从实际的Python文件中解析数据
    // 目前返回模拟数据
    const mockTableData: TableData = {
      moduleId,
      structure: {
        'LTHX': {
          'SIDE': { 1600: 'Offset', 2000: 'Offset', 3000: 'Offset' },
          'REAR': {}
        },
        'EVIK': {
          'SIDE': { 400: null, 630: 'Offset', 800: 'Offset' },
          'REAR': { 400: 0, 630: 'R1', 800: 'R2' }
        }
      },
      rules: {
        'R1': {
          type: 'rule',
          ruleId: 'R1',
          description: 'GOST标准规则',
          conditions: [
            {
              condition: 'std == "GOST 33984.1"',
              value: null,
              description: 'GOST标准时返回null'
            },
            {
              condition: 'std != "GOST 33984.1"',
              value: 0,
              description: '非GOST标准时返回0'
            }
          ]
        }
      },
      metadata: {
        version: '1.0.0',
        lastModified: '2024-01-15T10:30:00Z',
        modifiedBy: 'system',
        description: `${moduleId} 查表数据`
      }
    };

    return {
      code: 0,
      msg: '获取成功',
      data: mockTableData,
    };
  }

  // 修改数据
  static async modifyData(request: DataModificationRequest): Promise<DataModificationApiResponse> {
    await delay(1000);
    
    // 模拟数据修改
    const response: DataModificationResponse = {
      success: true,
      message: '数据修改成功',
      affectedPaths: [request.path],
    };

    return {
      code: 0,
      msg: '修改成功',
      data: response,
    };
  }

  // 验证数据
  static async validateData(moduleId: string, data: any): Promise<ValidationResponse> {
    await delay(500);
    
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: []
    };

    return {
      code: 0,
      msg: '验证完成',
      data: result,
    };
  }

  // 搜索数据
  static async searchData(request: DataSearchRequest): Promise<DataSearchResponse> {
    await delay(600);
    
    const results: DataSearchResult[] = [];
    
    return {
      code: 0,
      msg: '搜索完成',
      data: results,
    };
  }

  // 导出数据
  static async exportData(request: DataExportRequest): Promise<Blob> {
    await delay(1500);
    
    // 模拟导出
    const data = JSON.stringify({ modules: request.moduleIds }, null, 2);
    return new Blob([data], { type: 'application/json' });
  }

  // 导入数据
  static async importData(request: DataImportRequest): Promise<DataModificationApiResponse> {
    await delay(2000);
    
    const response: DataModificationResponse = {
      success: true,
      message: '数据导入成功',
      affectedPaths: [],
    };

    return {
      code: 0,
      msg: '导入成功',
      data: response,
    };
  }

  // 获取数据变更历史
  static async getChangeHistory(
    moduleId: string, 
    pagination: PaginationRequest
  ): Promise<PaginatedResponse<DataChangeHistory>> {
    await delay(400);
    
    return {
      data: [],
      pagination: {
        page: pagination.page,
        pageSize: pagination.pageSize,
        total: 0,
        totalPages: 0,
      }
    };
  }

  // 获取数据统计
  static async getStatistics(moduleId: string): Promise<DataStatistics> {
    await delay(300);
    
    const module = await this.getDataModule(moduleId);
    
    return {
      moduleId,
      totalRecords: module?.recordCount || 0,
      dataTypes: {
        'string': 15,
        'number': 20,
        'object': 10,
        'null': 5
      },
      lastModified: module?.lastModified || new Date().toISOString(),
      averageDepth: 2.5,
      maxDepth: 4,
    };
  }
}

// 导出便捷方法
export const {
  getDataModules,
  getDataModule,
  getTableData,
  modifyData,
  validateData,
  searchData,
  exportData,
  importData,
  getChangeHistory,
  getStatistics,
} = DataManagementAPI;
