# 数据管理模块文档

## 概述

数据管理模块是电梯设计计算系统的核心组件之一，专门用于管理和维护系统中的查表模块数据。该模块提供了完整的CRUD（创建、读取、更新、删除）功能，支持多种数据结构类型，并具备强大的数据验证和错误处理机制。

## 功能特性

### 🎯 核心功能
- **数据查看**: 支持树形和表格两种视图模式
- **数据编辑**: 支持创建、更新、删除操作
- **数据验证**: 完整的数据验证和错误处理
- **搜索过滤**: 强大的搜索和分类过滤功能
- **统计分析**: 详细的数据统计和分析报告

### 📊 支持的数据类型
- **简单类型**: 字符串、数字、布尔值、空值
- **嵌套对象**: 多层嵌套的字典结构
- **动态规则**: 基于条件的动态查询规则
- **混合类型**: 包含多种数据类型的复合结构

### 🔧 查表模块支持
系统支持以下查表模块的数据管理：

| 模块ID | 名称 | 描述 | 数据类型 |
|--------|------|------|----------|
| C0_table_query | 门中心偏移量查询 | 查询门中心相对于轿厢中心的偏移量 | 混合 |
| CD0_table_query | 标准轿厢深度查询 | 查询标准轿厢深度参数 | 嵌套 |
| CSD_table_query | 轿厢安全距离查询 | 查询轿厢安全距离相关参数 | 规则 |
| CW0_table_query | 标准轿厢宽度查询 | 查询标准轿厢宽度参数 | 嵌套 |
| CWGB_table_query | 对重导轨支架查询 | 查询对重导轨支架相关参数 | 简单 |
| CWG_table_query | 对重导轨间距查询 | 查询对重导轨间距参数 | 嵌套 |
| CWe_table_query | 对重有效宽度查询 | 查询对重有效宽度参数 | 嵌套 |
| Capacity_query | 载重范围查询 | 查询各电梯型号的载重范围 | 简单 |
| Car_table_query | 轿厢尺寸限制查询 | 查询轿厢尺寸的最小最大限制 | 规则 |
| DO0_table_query | 标准开门类型查询 | 查询标准开门类型参数 | 嵌套 |

## 使用指南

### 访问数据管理
1. 登录系统后，进入"系统设置"菜单
2. 点击"数据管理"选项
3. 系统将显示所有可用的查表模块

### 查看数据模块
1. 在数据管理主页面，浏览所有模块卡片
2. 使用搜索框快速查找特定模块
3. 使用分类过滤器按类型筛选模块
4. 点击"查看详情"按钮进入模块详情页

### 编辑数据
1. 在模块详情页，切换到"数据查看"标签
2. 在树形视图中找到要编辑的数据项
3. 点击数据项旁边的编辑按钮
4. 在弹出的编辑对话框中修改数据
5. 系统会自动验证数据格式和完整性
6. 点击"保存"完成修改

### 创建新数据项
1. 在数据树形视图中，找到要添加子项的节点
2. 点击"添加项"按钮
3. 选择数据类型（字符串、数字、对象、规则等）
4. 填写相应的数据内容
5. 系统验证通过后保存新数据项

### 删除数据项
1. 选择要删除的数据项
2. 点击删除按钮
3. 在确认对话框中确认删除操作
4. 系统会检查删除操作的安全性

## 数据结构说明

### 基础数据类型
```typescript
// 字符串类型
"LTHX": "Offset"

// 数字类型
"1600": 2450

// 布尔类型
"enabled": true

// 空值类型
"value": null
```

### 嵌套对象结构
```typescript
{
  "LTHX": {
    "SIDE": {
      "1600": "Offset",
      "2000": "Offset",
      "3000": "Offset"
    },
    "REAR": {}
  }
}
```

### 动态规则结构
```typescript
{
  "type": "rule",
  "ruleId": "R1",
  "description": "GOST标准规则",
  "conditions": [
    {
      "condition": "std == \"GOST 33984.1\"",
      "value": null,
      "description": "GOST标准时返回null"
    },
    {
      "condition": "std != \"GOST 33984.1\"",
      "value": 0,
      "description": "非GOST标准时返回0"
    }
  ]
}
```

## 数据验证规则

### 通用验证
- 数据类型一致性检查
- 数据格式规范验证
- 数据完整性验证
- 安全性检查（防止注入攻击）

### 规则验证
- 规则ID格式检查（必须以字母开头）
- 条件表达式语法验证
- 返回值类型检查
- 条件逻辑完整性验证

### 对象验证
- 键名规范检查
- 嵌套深度限制
- 循环引用检测
- 数据量大小限制

## 错误处理

### 验证错误
系统会显示详细的验证错误信息，包括：
- 错误位置（数据路径）
- 错误描述
- 错误代码
- 修复建议

### 操作错误
- 网络连接错误
- 权限不足错误
- 数据冲突错误
- 系统内部错误

### 警告信息
系统会提供友好的警告信息：
- 数据质量警告
- 性能优化建议
- 最佳实践提示

## 安全考虑

### 数据安全
- 输入数据验证和清理
- SQL注入防护
- XSS攻击防护
- 数据访问权限控制

### 操作安全
- 操作日志记录
- 数据备份机制
- 回滚功能支持
- 并发操作控制

## 性能优化

### 前端优化
- 虚拟滚动支持大数据量
- 懒加载减少初始加载时间
- 缓存机制提高响应速度
- 防抖搜索减少请求频率

### 后端优化
- 分页查询支持
- 索引优化
- 缓存策略
- 异步处理

## 技术架构

### 前端技术栈
- **框架**: Next.js 14 with App Router
- **UI组件**: Shadcn/ui + Radix UI
- **样式**: Tailwind CSS
- **状态管理**: React useState + useEffect
- **类型安全**: TypeScript

### 组件架构
```
src/
├── api/data-management/          # API层
│   ├── api.ts                   # API服务
│   └── types.ts                 # 类型定义
├── app/dashboard/system-settings/data-management/  # 页面层
│   ├── page.tsx                 # 主页面
│   └── [module]/page.tsx        # 模块详情页
└── components/data-management/   # 组件层
    ├── DataViewer.tsx           # 数据查看器
    ├── DataEditor.tsx           # 数据编辑器
    ├── DataStatistics.tsx       # 数据统计
    └── AppleStyleLoading.tsx    # 加载组件
```

## 未来规划

### 短期目标
- [ ] 数据导入导出功能
- [ ] 批量操作支持
- [ ] 数据变更历史记录
- [ ] 高级搜索功能

### 长期目标
- [ ] 实时协作编辑
- [ ] 数据版本控制
- [ ] 自动化测试集成
- [ ] 多语言支持

## 常见问题

### Q: 如何备份数据？
A: 目前支持单个模块的JSON格式导出，完整的备份功能正在开发中。

### Q: 数据修改后如何撤销？
A: 当前版本不支持撤销功能，建议在重要修改前先导出备份。

### Q: 支持哪些数据格式导入？
A: 目前支持JSON格式，未来将支持CSV和Excel格式。

### Q: 如何处理大量数据？
A: 系统采用分页加载和虚拟滚动技术，可以处理大量数据而不影响性能。

## 联系支持

如果您在使用过程中遇到问题或有改进建议，请联系开发团队：
- 邮箱: <EMAIL>
- 内部工单系统: 提交技术支持工单

---

*最后更新: 2024年1月*
