"use client";

import React from 'react';
import { cn } from '@/lib/utils';

interface AppleStyleLoadingProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  text?: string;
}

export function AppleStyleLoading({ 
  size = 'md', 
  className,
  text = '加载中...' 
}: AppleStyleLoadingProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  };

  return (
    <div className={cn("flex flex-col items-center justify-center gap-3", className)}>
      <div className={cn(
        "relative",
        sizeClasses[size]
      )}>
        {/* 苹果风格的圆形加载动画 */}
        <div className="absolute inset-0 rounded-full border-2 border-gray-200"></div>
        <div className="absolute inset-0 rounded-full border-2 border-blue-500 border-t-transparent animate-spin"></div>
      </div>
      {text && (
        <span className={cn(
          "text-gray-600 font-medium",
          textSizeClasses[size]
        )}>
          {text}
        </span>
      )}
    </div>
  );
}

// 全屏加载遮罩
export function AppleStyleLoadingOverlay({ 
  text = '处理中...',
  className 
}: { 
  text?: string;
  className?: string;
}) {
  return (
    <div className={cn(
      "fixed inset-0 bg-white/80 backdrop-blur-sm z-50 flex items-center justify-center",
      className
    )}>
      <div className="bg-white rounded-2xl shadow-lg p-8 flex flex-col items-center gap-4">
        <AppleStyleLoading size="lg" text={text} />
      </div>
    </div>
  );
}

// 卡片内加载状态
export function AppleStyleCardLoading({ 
  text = '加载中...',
  className 
}: { 
  text?: string;
  className?: string;
}) {
  return (
    <div className={cn(
      "flex items-center justify-center py-12",
      className
    )}>
      <AppleStyleLoading size="md" text={text} />
    </div>
  );
}
