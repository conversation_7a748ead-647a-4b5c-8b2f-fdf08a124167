"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  IconChevronDown, 
  IconChevronRight, 
  IconEdit, 
  IconTrash, 
  IconPlus,
  IconSearch,
  IconCode,
  IconTable
} from '@tabler/icons-react';
import { TableData, DataValue, DataObject, DataRule } from '@/api/data-management/types';
import { DataEditor } from './DataEditor';

interface DataViewerProps {
  data: TableData;
  onEdit?: (path: string[], value: DataValue) => void;
  onDelete?: (path: string[]) => void;
  onAdd?: (path: string[]) => void;
}

interface DataNodeProps {
  value: DataValue;
  path: string[];
  level: number;
  onEdit?: (path: string[], value: DataValue) => void;
  onDelete?: (path: string[]) => void;
  onAdd?: (path: string[]) => void;
}

// 数据节点组件
function DataNode({ value, path, level, onEdit, onDelete, onAdd }: DataNodeProps) {
  const [isExpanded, setIsExpanded] = useState(level < 2); // 默认展开前两层
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState('');

  const indent = level * 20;
  const key = path[path.length - 1] || 'root';

  // 处理编辑
  const handleEdit = () => {
    setEditValue(String(value));
    setIsEditing(true);
  };

  const handleSaveEdit = () => {
    if (onEdit) {
      let newValue: DataValue;
      try {
        // 尝试解析为数字
        if (!isNaN(Number(editValue)) && editValue !== '') {
          newValue = Number(editValue);
        } else if (editValue === 'null') {
          newValue = null;
        } else if (editValue === 'true') {
          newValue = true;
        } else if (editValue === 'false') {
          newValue = false;
        } else {
          newValue = editValue;
        }
        onEdit(path, newValue);
      } catch (error) {
        console.error('编辑值失败:', error);
      }
    }
    setIsEditing(false);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditValue('');
  };

  // 渲染值类型标识
  const renderValueType = (val: DataValue) => {
    if (val === null) return <Badge variant="outline" className="text-xs">null</Badge>;
    if (typeof val === 'boolean') return <Badge variant="outline" className="text-xs">bool</Badge>;
    if (typeof val === 'number') return <Badge variant="outline" className="text-xs">num</Badge>;
    if (typeof val === 'string') return <Badge variant="outline" className="text-xs">str</Badge>;
    if (typeof val === 'object' && 'type' in val && val.type === 'rule') {
      return <Badge variant="secondary" className="text-xs">rule</Badge>;
    }
    return <Badge variant="outline" className="text-xs">obj</Badge>;
  };

  // 渲染规则
  const renderRule = (rule: DataRule) => (
    <div className="ml-4 p-3 bg-amber-50 border border-amber-200 rounded-md">
      <div className="flex items-center gap-2 mb-2">
        <IconCode className="h-4 w-4 text-amber-600" />
        <span className="font-medium text-amber-800">规则: {rule.ruleId}</span>
      </div>
      <p className="text-sm text-amber-700 mb-2">{rule.description}</p>
      <div className="space-y-1">
        {rule.conditions.map((condition, index) => (
          <div key={index} className="text-xs bg-white p-2 rounded border">
            <code className="text-blue-600">{condition.condition}</code>
            <span className="mx-2">→</span>
            <span className="font-medium">{String(condition.value)}</span>
            {condition.description && (
              <p className="text-gray-600 mt-1">{condition.description}</p>
            )}
          </div>
        ))}
      </div>
    </div>
  );

  // 如果是规则类型
  if (typeof value === 'object' && value !== null && 'type' in value && value.type === 'rule') {
    return (
      <div style={{ marginLeft: indent }}>
        <div className="flex items-center gap-2 py-1">
          <span className="font-medium text-amber-700">{key}:</span>
          {renderValueType(value)}
          <div className="flex gap-1 ml-auto">
            <Button size="sm" variant="ghost" onClick={handleEdit}>
              <IconEdit className="h-3 w-3" />
            </Button>
            {onDelete && (
              <Button size="sm" variant="ghost" onClick={() => onDelete(path)}>
                <IconTrash className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>
        {renderRule(value as DataRule)}
      </div>
    );
  }

  // 如果是对象类型
  if (typeof value === 'object' && value !== null && !('type' in value)) {
    const obj = value as DataObject;
    const hasChildren = Object.keys(obj).length > 0;

    return (
      <div style={{ marginLeft: indent }}>
        <div className="flex items-center gap-2 py-1 hover:bg-gray-50 rounded">
          {hasChildren && (
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setIsExpanded(!isExpanded)}
              className="p-0 h-auto"
            >
              {isExpanded ? (
                <IconChevronDown className="h-4 w-4" />
              ) : (
                <IconChevronRight className="h-4 w-4" />
              )}
            </Button>
          )}
          {!hasChildren && <div className="w-4" />}
          <span className="font-medium">{key}:</span>
          {renderValueType(value)}
          <span className="text-sm text-gray-500">({Object.keys(obj).length} 项)</span>
          <div className="flex gap-1 ml-auto">
            {onAdd && (
              <Button size="sm" variant="ghost" onClick={() => onAdd(path)}>
                <IconPlus className="h-3 w-3" />
              </Button>
            )}
            {onDelete && (
              <Button size="sm" variant="ghost" onClick={() => onDelete(path)}>
                <IconTrash className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>
        {isExpanded && hasChildren && (
          <div className="border-l border-gray-200 ml-2">
            {Object.entries(obj).map(([childKey, childValue]) => (
              <DataNode
                key={childKey}
                value={childValue}
                path={[...path, childKey]}
                level={level + 1}
                onEdit={onEdit}
                onDelete={onDelete}
                onAdd={onAdd}
              />
            ))}
          </div>
        )}
      </div>
    );
  }

  // 基础类型值
  return (
    <div style={{ marginLeft: indent }}>
      <div className="flex items-center gap-2 py-1 hover:bg-gray-50 rounded">
        <div className="w-4" />
        <span className="font-medium">{key}:</span>
        {renderValueType(value)}
        {isEditing ? (
          <div className="flex items-center gap-2 flex-1">
            <Input
              value={editValue}
              onChange={(e) => setEditValue(e.target.value)}
              className="h-6 text-sm"
              onKeyDown={(e) => {
                if (e.key === 'Enter') handleSaveEdit();
                if (e.key === 'Escape') handleCancelEdit();
              }}
              autoFocus
            />
            <Button size="sm" variant="ghost" onClick={handleSaveEdit}>
              ✓
            </Button>
            <Button size="sm" variant="ghost" onClick={handleCancelEdit}>
              ✕
            </Button>
          </div>
        ) : (
          <>
            <span className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
              {String(value)}
            </span>
            <div className="flex gap-1 ml-auto">
              <Button size="sm" variant="ghost" onClick={handleEdit}>
                <IconEdit className="h-3 w-3" />
              </Button>
              {onDelete && (
                <Button size="sm" variant="ghost" onClick={() => onDelete(path)}>
                  <IconTrash className="h-3 w-3" />
                </Button>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
}

export function DataViewer({ data, onEdit, onDelete, onAdd }: DataViewerProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'tree' | 'table'>('tree');
  const [editorOpen, setEditorOpen] = useState(false);
  const [editorMode, setEditorMode] = useState<'create' | 'edit' | 'delete'>('edit');
  const [editorPath, setEditorPath] = useState<string[]>([]);
  const [editorValue, setEditorValue] = useState<DataValue>();

  // 搜索过滤逻辑
  const filterData = (obj: DataObject, term: string): DataObject => {
    if (!term) return obj;
    
    const filtered: DataObject = {};
    
    Object.entries(obj).forEach(([key, value]) => {
      if (key.toLowerCase().includes(term.toLowerCase()) ||
          String(value).toLowerCase().includes(term.toLowerCase())) {
        filtered[key] = value;
      } else if (typeof value === 'object' && value !== null && !('type' in value)) {
        const filteredChild = filterData(value as DataObject, term);
        if (Object.keys(filteredChild).length > 0) {
          filtered[key] = filteredChild;
        }
      }
    });
    
    return filtered;
  };

  const filteredStructure = filterData(data.structure, searchTerm);

  // 处理编辑器操作
  const handleEdit = (path: string[], value: DataValue) => {
    setEditorMode('edit');
    setEditorPath(path);
    setEditorValue(value);
    setEditorOpen(true);
  };

  const handleAdd = (path: string[]) => {
    setEditorMode('create');
    setEditorPath(path);
    setEditorValue(undefined);
    setEditorOpen(true);
  };

  const handleDelete = (path: string[], value: DataValue) => {
    setEditorMode('delete');
    setEditorPath(path);
    setEditorValue(value);
    setEditorOpen(true);
  };

  const handleSave = async (path: string[], value: DataValue) => {
    if (onEdit) {
      await onEdit(path, value);
    }
  };

  const handleDeleteConfirm = async (path: string[]) => {
    if (onDelete) {
      await onDelete(path);
    }
  };

  return (
    <div className="space-y-4">
      {/* 工具栏 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="relative">
            <IconSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索键或值..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-64"
            />
          </div>
          <div className="flex gap-1">
            <Button
              size="sm"
              variant={viewMode === 'tree' ? 'default' : 'outline'}
              onClick={() => setViewMode('tree')}
            >
              树形视图
            </Button>
            <Button
              size="sm"
              variant={viewMode === 'table' ? 'default' : 'outline'}
              onClick={() => setViewMode('table')}
            >
              表格视图
            </Button>
          </div>
        </div>
        <div className="flex gap-2">
          {onAdd && (
            <Button size="sm" onClick={() => onAdd([])}>
              <IconPlus className="h-4 w-4 mr-1" />
              添加项
            </Button>
          )}
        </div>
      </div>

      {/* 数据结构展示 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <IconTable className="h-5 w-5" />
            数据结构
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="max-h-96 overflow-auto">
            <DataNode
              value={filteredStructure}
              path={[]}
              level={0}
              onEdit={handleEdit}
              onDelete={(path) => {
                const getValue = (obj: any, pathArray: string[]): any => {
                  return pathArray.reduce((current, key) => current?.[key], obj);
                };
                const value = getValue(data.structure, path);
                handleDelete(path, value);
              }}
              onAdd={handleAdd}
            />
          </div>
        </CardContent>
      </Card>

      {/* 规则展示 */}
      {data.rules && Object.keys(data.rules).length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <IconCode className="h-5 w-5" />
              动态规则
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(data.rules).map(([ruleId, rule]) => (
                <div key={ruleId}>
                  {renderRule(rule)}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 元数据 */}
      <Card>
        <CardHeader>
          <CardTitle>元数据</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">版本:</span>
              <span className="ml-2 font-mono">{data.metadata.version}</span>
            </div>
            <div>
              <span className="text-muted-foreground">最后修改:</span>
              <span className="ml-2">{new Date(data.metadata.lastModified).toLocaleString()}</span>
            </div>
            <div>
              <span className="text-muted-foreground">修改者:</span>
              <span className="ml-2">{data.metadata.modifiedBy}</span>
            </div>
            <div className="col-span-2">
              <span className="text-muted-foreground">描述:</span>
              <span className="ml-2">{data.metadata.description}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 数据编辑器 */}
      <DataEditor
        open={editorOpen}
        onOpenChange={setEditorOpen}
        mode={editorMode}
        path={editorPath}
        currentValue={editorValue}
        onSave={handleSave}
        onDelete={handleDeleteConfirm}
      />
    </div>
  );
}
