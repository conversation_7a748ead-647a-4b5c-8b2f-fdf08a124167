"use client";

import React from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import {
  IconChartBar,
  IconDatabase,
  IconClock,
  IconStack,
  IconTrendingUp
} from '@tabler/icons-react';
import { DataStatistics as DataStatsType } from '@/api/data-management/types';

interface DataStatisticsProps {
  statistics: DataStatsType;
}

export function DataStatistics({ statistics }: DataStatisticsProps) {
  // 计算数据类型分布百分比
  const totalTypes = Object.values(statistics.dataTypes).reduce((sum, count) => sum + count, 0);
  const typePercentages = Object.entries(statistics.dataTypes).map(([type, count]) => ({
    type,
    count,
    percentage: totalTypes > 0 ? (count / totalTypes) * 100 : 0
  }));

  // 数据类型颜色映射
  const typeColors = {
    string: 'bg-blue-500',
    number: 'bg-green-500',
    object: 'bg-purple-500',
    null: 'bg-gray-500',
    boolean: 'bg-orange-500',
    rule: 'bg-red-500'
  };

  // 数据类型图标映射
  const typeIcons = {
    string: '📝',
    number: '🔢',
    object: '🗂️',
    null: '⭕',
    boolean: '✅',
    rule: '⚙️'
  };

  return (
    <div className="space-y-6">
      {/* 概览统计 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">总记录数</p>
                <p className="text-2xl font-bold">{statistics.totalRecords}</p>
              </div>
              <IconDatabase className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">数据类型</p>
                <p className="text-2xl font-bold">{Object.keys(statistics.dataTypes).length}</p>
              </div>
              <IconStack className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">平均深度</p>
                <p className="text-2xl font-bold">{statistics.averageDepth.toFixed(1)}</p>
              </div>
              <IconTrendingUp className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">最大深度</p>
                <p className="text-2xl font-bold">{statistics.maxDepth}</p>
              </div>
              <IconChartBar className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 数据类型分布 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <IconChartBar className="h-5 w-5" />
            数据类型分布
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {typePercentages.map(({ type, count, percentage }) => (
              <div key={type} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span className="text-lg">{typeIcons[type as keyof typeof typeIcons] || '📄'}</span>
                    <span className="font-medium capitalize">{type}</span>
                    <Badge variant="outline">{count} 项</Badge>
                  </div>
                  <span className="text-sm text-muted-foreground">
                    {percentage.toFixed(1)}%
                  </span>
                </div>
                <Progress 
                  value={percentage} 
                  className="h-2"
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 结构复杂度分析 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <IconStack className="h-5 w-5" />
            结构复杂度分析
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 深度分析 */}
            <div className="space-y-4">
              <h4 className="font-medium">嵌套深度</h4>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">平均深度</span>
                  <span className="font-mono">{statistics.averageDepth.toFixed(2)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">最大深度</span>
                  <span className="font-mono">{statistics.maxDepth}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">复杂度评级</span>
                  <Badge variant={statistics.maxDepth > 4 ? "destructive" : statistics.maxDepth > 2 ? "secondary" : "default"}>
                    {statistics.maxDepth > 4 ? "高" : statistics.maxDepth > 2 ? "中" : "低"}
                  </Badge>
                </div>
              </div>
            </div>

            {/* 数据质量 */}
            <div className="space-y-4">
              <h4 className="font-medium">数据质量</h4>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">空值比例</span>
                  <span className="font-mono">
                    {totalTypes > 0 ? ((statistics.dataTypes.null || 0) / totalTypes * 100).toFixed(1) : 0}%
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">对象比例</span>
                  <span className="font-mono">
                    {totalTypes > 0 ? ((statistics.dataTypes.object || 0) / totalTypes * 100).toFixed(1) : 0}%
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">数据完整性</span>
                  <Badge variant="default">
                    {totalTypes > 0 && (statistics.dataTypes.null || 0) / totalTypes < 0.1 ? "良好" : "一般"}
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 最后更新信息 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <IconClock className="h-5 w-5" />
            更新信息
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-muted-foreground">最后更新时间</p>
              <p className="font-medium">
                {new Date(statistics.lastModified).toLocaleString()}
              </p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">数据新鲜度</p>
              <Badge variant="outline">
                {(() => {
                  const daysDiff = Math.floor((Date.now() - new Date(statistics.lastModified).getTime()) / (1000 * 60 * 60 * 24));
                  if (daysDiff === 0) return "今天";
                  if (daysDiff === 1) return "昨天";
                  if (daysDiff < 7) return `${daysDiff} 天前`;
                  if (daysDiff < 30) return `${Math.floor(daysDiff / 7)} 周前`;
                  return `${Math.floor(daysDiff / 30)} 月前`;
                })()}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 建议和优化 */}
      <Card>
        <CardHeader>
          <CardTitle>优化建议</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {statistics.maxDepth > 4 && (
              <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                <p className="text-sm text-yellow-800">
                  ⚠️ 数据结构嵌套过深（{statistics.maxDepth} 层），建议考虑扁平化处理以提高查询性能。
                </p>
              </div>
            )}
            
            {totalTypes > 0 && (statistics.dataTypes.null || 0) / totalTypes > 0.2 && (
              <div className="p-3 bg-orange-50 border border-orange-200 rounded-md">
                <p className="text-sm text-orange-800">
                  💡 空值比例较高（{((statistics.dataTypes.null || 0) / totalTypes * 100).toFixed(1)}%），建议检查数据完整性。
                </p>
              </div>
            )}
            
            {statistics.totalRecords > 1000 && (
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                <p className="text-sm text-blue-800">
                  📈 数据量较大（{statistics.totalRecords} 条记录），建议考虑分页加载和索引优化。
                </p>
              </div>
            )}
            
            {Object.keys(statistics.dataTypes).length === 1 && (
              <div className="p-3 bg-green-50 border border-green-200 rounded-md">
                <p className="text-sm text-green-800">
                  ✅ 数据类型统一，结构清晰，便于维护和查询。
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
