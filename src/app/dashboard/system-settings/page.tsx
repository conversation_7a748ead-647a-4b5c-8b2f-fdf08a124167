"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { IconLanguage, IconSettings, IconArrowRight } from '@tabler/icons-react';
import Link from 'next/link';

const settingsModules = [
  {
    title: '多语言管理',
    description: '管理系统中的多语言字典，支持中文、英文和越南语',
    icon: IconLanguage,
    href: '/dashboard/system-settings/language-management',
    color: 'bg-blue-500',
  },
  {
    title: '数据管理',
    description: '管理电梯设计计算系统的查表模块数据，支持查看、编辑和维护',
    icon: IconSettings,
    href: '/dashboard/system-settings/data-management',
    color: 'bg-green-500',
  },
  // 可以在这里添加更多的设置模块
  // {
  //   title: '用户权限',
  //   description: '管理用户角色和权限设置',
  //   icon: IconShield,
  //   href: '/dashboard/system-settings/user-permissions',
  //   color: 'bg-red-500',
  // },
];

export default function SystemSettingsPage() {
  return (
    <div className="space-y-6 p-5">
      <div className="flex items-center gap-2">
        <IconSettings className="h-6 w-6" />
        <h1 className="text-2xl font-bold">系统设置</h1>
      </div>
      
      <p className="text-muted-foreground">
        管理系统的各项配置和设置，确保系统正常运行
      </p>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {settingsModules.map((module) => {
          const IconComponent = module.icon;
          return (
            <Card key={module.href} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-lg ${module.color}`}>
                    <IconComponent className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-lg">{module.title}</CardTitle>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <CardDescription className="mb-4">
                  {module.description}
                </CardDescription>
                <Link href={module.href}>
                  <Button className="w-full group">
                    进入设置
                    <IconArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </Link>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {settingsModules.length === 1 && (
        <Card className="bg-muted/50">
          <CardContent className="pt-6">
            <div className="text-center">
              <IconSettings className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">更多设置模块即将推出</h3>
              <p className="text-muted-foreground">
                我们正在开发更多的系统设置功能，敬请期待
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 