"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { 
  IconTestPipe, 
  IconCheck, 
  IconX,
  IconPlay,
  IconRefresh
} from '@tabler/icons-react';
import { getDataModules, getTableData, modifyData, validateData } from '@/api/data-management/api';
import { validateDataValue } from '@/lib/data-validation';

interface TestResult {
  name: string;
  status: 'pending' | 'success' | 'error';
  message: string;
  duration?: number;
}

export default function DataManagementTestPage() {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const tests = [
    {
      name: '获取数据模块列表',
      test: async () => {
        const response = await getDataModules();
        if (response.code === 0 && response.data.length > 0) {
          return { success: true, message: `成功获取 ${response.data.length} 个模块` };
        }
        throw new Error('获取模块列表失败');
      }
    },
    {
      name: '获取表格数据',
      test: async () => {
        const response = await getTableData('C0_table_query');
        if (response.code === 0 && response.data) {
          return { success: true, message: '成功获取表格数据' };
        }
        throw new Error('获取表格数据失败');
      }
    },
    {
      name: '数据验证功能',
      test: async () => {
        const testData = {
          "LTHX": {
            "SIDE": { 1600: "Offset", 2000: "Offset" }
          }
        };
        const validation = validateDataValue(testData);
        if (validation.isValid) {
          return { success: true, message: '数据验证通过' };
        }
        throw new Error(`数据验证失败: ${validation.errors[0]?.message}`);
      }
    },
    {
      name: '数据修改操作',
      test: async () => {
        const response = await modifyData({
          moduleId: 'C0_table_query',
          operation: 'update',
          path: ['test'],
          value: 'test_value'
        });
        if (response.code === 0) {
          return { success: true, message: '数据修改成功' };
        }
        throw new Error('数据修改失败');
      }
    },
    {
      name: '规则验证测试',
      test: async () => {
        const ruleData = {
          type: 'rule' as const,
          ruleId: 'TEST_RULE',
          description: '测试规则',
          conditions: [
            {
              condition: 'value > 0',
              value: true,
              description: '值大于0时返回true'
            }
          ]
        };
        const validation = validateDataValue(ruleData);
        if (validation.isValid) {
          return { success: true, message: '规则验证通过' };
        }
        throw new Error(`规则验证失败: ${validation.errors[0]?.message}`);
      }
    }
  ];

  const runTest = async (test: typeof tests[0], index: number) => {
    const startTime = Date.now();
    
    setTestResults(prev => prev.map((result, i) => 
      i === index ? { ...result, status: 'pending' as const } : result
    ));

    try {
      const result = await test.test();
      const duration = Date.now() - startTime;
      
      setTestResults(prev => prev.map((result, i) => 
        i === index ? {
          ...result,
          status: 'success' as const,
          message: result.message,
          duration
        } : result
      ));
    } catch (error) {
      const duration = Date.now() - startTime;
      
      setTestResults(prev => prev.map((result, i) => 
        i === index ? {
          ...result,
          status: 'error' as const,
          message: error instanceof Error ? error.message : '测试失败',
          duration
        } : result
      ));
    }
  };

  const runAllTests = async () => {
    setIsRunning(true);
    
    // 初始化测试结果
    setTestResults(tests.map(test => ({
      name: test.name,
      status: 'pending' as const,
      message: '等待执行...'
    })));

    // 依次执行所有测试
    for (let i = 0; i < tests.length; i++) {
      await runTest(tests[i], i);
      // 添加小延迟以便观察测试过程
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    setIsRunning(false);
    
    const successCount = testResults.filter(r => r.status === 'success').length;
    const totalCount = tests.length;
    
    if (successCount === totalCount) {
      toast.success(`所有测试通过 (${successCount}/${totalCount})`);
    } else {
      toast.error(`部分测试失败 (${successCount}/${totalCount})`);
    }
  };

  const resetTests = () => {
    setTestResults([]);
    setIsRunning(false);
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <IconCheck className="h-5 w-5 text-green-500" />;
      case 'error':
        return <IconX className="h-5 w-5 text-red-500" />;
      case 'pending':
        return <div className="h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />;
      default:
        return <div className="h-5 w-5 border-2 border-gray-300 rounded-full" />;
    }
  };

  const getStatusBadge = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <Badge className="bg-green-100 text-green-800 border-green-200">通过</Badge>;
      case 'error':
        return <Badge className="bg-red-100 text-red-800 border-red-200">失败</Badge>;
      case 'pending':
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">运行中</Badge>;
      default:
        return <Badge variant="outline">待运行</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <IconTestPipe className="h-6 w-6" />
            数据管理模块测试
          </h1>
          <p className="text-muted-foreground">
            验证数据管理模块的核心功能
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={resetTests} disabled={isRunning}>
            <IconRefresh className="h-4 w-4 mr-1" />
            重置
          </Button>
          <Button onClick={runAllTests} disabled={isRunning}>
            <IconPlay className="h-4 w-4 mr-1" />
            {isRunning ? '运行中...' : '运行所有测试'}
          </Button>
        </div>
      </div>

      {/* 测试概览 */}
      <Card>
        <CardHeader>
          <CardTitle>测试概览</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold">{tests.length}</p>
              <p className="text-sm text-muted-foreground">总测试数</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">
                {testResults.filter(r => r.status === 'success').length}
              </p>
              <p className="text-sm text-muted-foreground">通过</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-red-600">
                {testResults.filter(r => r.status === 'error').length}
              </p>
              <p className="text-sm text-muted-foreground">失败</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">
                {testResults.filter(r => r.status === 'pending').length}
              </p>
              <p className="text-sm text-muted-foreground">运行中</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 测试结果列表 */}
      <Card>
        <CardHeader>
          <CardTitle>测试结果</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {tests.map((test, index) => {
              const result = testResults[index];
              return (
                <div
                  key={index}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center gap-3">
                    {getStatusIcon(result?.status)}
                    <div>
                      <h3 className="font-medium">{test.name}</h3>
                      <p className="text-sm text-muted-foreground">
                        {result?.message || '等待执行...'}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    {result?.duration && (
                      <span className="text-xs text-muted-foreground">
                        {result.duration}ms
                      </span>
                    )}
                    {getStatusBadge(result?.status)}
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => runTest(test, index)}
                      disabled={isRunning}
                    >
                      运行
                    </Button>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
