// 数据管理模块类型定义

import { ResponseData } from '@/api/types';

// 查表模块基础信息
export interface DataModule {
  id: string;
  name: string;
  description: string;
  fileName: string;
  category: 'basic' | 'dimension' | 'safety' | 'door' | 'capacity';
  dataType: 'simple' | 'nested' | 'rules' | 'mixed';
  lastModified: string;
  recordCount: number;
}

// 数据项类型
export type DataValue = string | number | boolean | null | DataObject | DataRule;

// 数据对象（嵌套字典）
export interface DataObject {
  [key: string]: DataValue;
}

// 动态规则定义
export interface DataRule {
  type: 'rule';
  ruleId: string;
  description: string;
  conditions: RuleCondition[];
}

// 规则条件
export interface RuleCondition {
  condition: string; // lambda函数字符串
  value: DataValue;
  description?: string;
}

// 查表数据结构
export interface TableData {
  moduleId: string;
  structure: DataObject;
  rules?: Record<string, DataRule>;
  metadata: {
    version: string;
    lastModified: string;
    modifiedBy: string;
    description: string;
  };
}

// 数据操作类型
export type DataOperation = 'create' | 'read' | 'update' | 'delete';

// 数据修改请求
export interface DataModificationRequest {
  moduleId: string;
  operation: DataOperation;
  path: string[]; // 数据路径，如 ['LTHX', 'SIDE', '1600']
  value?: DataValue;
  oldValue?: DataValue;
}

// 数据修改响应
export interface DataModificationResponse {
  success: boolean;
  message: string;
  affectedPaths: string[][];
  newData?: TableData;
}

// 数据验证结果
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

// 验证错误
export interface ValidationError {
  path: string[];
  message: string;
  code: string;
}

// 验证警告
export interface ValidationWarning {
  path: string[];
  message: string;
  suggestion?: string;
}

// 数据搜索请求
export interface DataSearchRequest {
  moduleId?: string;
  query: string;
  searchType: 'key' | 'value' | 'both';
  caseSensitive?: boolean;
}

// 数据搜索结果
export interface DataSearchResult {
  moduleId: string;
  matches: DataMatch[];
  totalCount: number;
}

// 搜索匹配项
export interface DataMatch {
  path: string[];
  key: string;
  value: DataValue;
  context: string;
}

// API响应类型
export type DataModuleListResponse = ResponseData<DataModule[]>;
export type TableDataResponse = ResponseData<TableData>;
export type DataModificationApiResponse = ResponseData<DataModificationResponse>;
export type ValidationResponse = ResponseData<ValidationResult>;
export type DataSearchResponse = ResponseData<DataSearchResult[]>;

// 数据导入导出
export interface DataExportRequest {
  moduleIds: string[];
  format: 'json' | 'csv' | 'excel';
  includeRules?: boolean;
}

export interface DataImportRequest {
  moduleId: string;
  data: string; // JSON字符串或文件内容
  format: 'json' | 'csv' | 'excel';
  mergeStrategy: 'replace' | 'merge' | 'append';
}

// 数据备份和恢复
export interface DataBackup {
  id: string;
  name: string;
  description: string;
  createdAt: string;
  moduleIds: string[];
  size: number;
}

export interface DataRestoreRequest {
  backupId: string;
  moduleIds?: string[];
  restoreStrategy: 'full' | 'selective';
}

// 数据变更历史
export interface DataChangeHistory {
  id: string;
  moduleId: string;
  operation: DataOperation;
  path: string[];
  oldValue: DataValue;
  newValue: DataValue;
  timestamp: string;
  userId: string;
  userName: string;
  description?: string;
}

// 分页请求
export interface PaginationRequest {
  page: number;
  pageSize: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 分页响应
export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}

// 数据统计信息
export interface DataStatistics {
  moduleId: string;
  totalRecords: number;
  dataTypes: Record<string, number>;
  lastModified: string;
  averageDepth: number;
  maxDepth: number;
}
