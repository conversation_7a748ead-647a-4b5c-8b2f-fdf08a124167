"use client";

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  IconAlertCircle, 
  IconCheck, 
  IconX,
  IconPlus,
  IconTrash
} from '@tabler/icons-react';
import { DataValue, DataObject, DataRule, RuleCondition } from '@/api/data-management/types';
import { toast } from 'sonner';
import { validateDataValue, validateDataModification } from '@/lib/data-validation';

interface DataEditorProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  mode: 'create' | 'edit' | 'delete';
  path: string[];
  currentValue?: DataValue;
  onSave: (path: string[], value: DataValue) => Promise<void>;
  onDelete?: (path: string[]) => Promise<void>;
}

export function DataEditor({
  open,
  onOpenChange,
  mode,
  path,
  currentValue,
  onSave,
  onDelete
}: DataEditorProps) {
  const [value, setValue] = useState<string>('');
  const [valueType, setValueType] = useState<'string' | 'number' | 'boolean' | 'null' | 'object' | 'rule'>('string');
  const [objectData, setObjectData] = useState<Record<string, string>>({});
  const [ruleData, setRuleData] = useState<{
    ruleId: string;
    description: string;
    conditions: Array<{ condition: string; value: string; description: string }>;
  }>({
    ruleId: '',
    description: '',
    conditions: [{ condition: '', value: '', description: '' }]
  });
  const [errors, setErrors] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // 初始化表单数据
  useEffect(() => {
    if (open && currentValue !== undefined) {
      initializeForm(currentValue);
    } else if (open && mode === 'create') {
      resetForm();
    }
  }, [open, currentValue, mode]);

  const initializeForm = (val: DataValue) => {
    if (val === null) {
      setValueType('null');
      setValue('');
    } else if (typeof val === 'boolean') {
      setValueType('boolean');
      setValue(String(val));
    } else if (typeof val === 'number') {
      setValueType('number');
      setValue(String(val));
    } else if (typeof val === 'string') {
      setValueType('string');
      setValue(val);
    } else if (typeof val === 'object' && 'type' in val && val.type === 'rule') {
      setValueType('rule');
      const rule = val as DataRule;
      setRuleData({
        ruleId: rule.ruleId,
        description: rule.description,
        conditions: rule.conditions.map(c => ({
          condition: c.condition,
          value: String(c.value),
          description: c.description || ''
        }))
      });
    } else if (typeof val === 'object') {
      setValueType('object');
      const obj = val as DataObject;
      const flatObj: Record<string, string> = {};
      Object.entries(obj).forEach(([key, value]) => {
        flatObj[key] = String(value);
      });
      setObjectData(flatObj);
    }
  };

  const resetForm = () => {
    setValue('');
    setValueType('string');
    setObjectData({});
    setRuleData({
      ruleId: '',
      description: '',
      conditions: [{ condition: '', value: '', description: '' }]
    });
    setErrors([]);
  };

  // 验证表单
  const validateForm = (): boolean => {
    const newErrors: string[] = [];

    try {
      // 转换为DataValue进行验证
      const dataValue = convertToDataValue();

      // 使用数据验证工具
      const validation = validateDataValue(dataValue, path);

      // 添加验证错误
      validation.errors.forEach(error => {
        newErrors.push(error.message);
      });

      // 验证数据修改操作
      const modificationValidation = validateDataModification(
        mode === 'create' ? 'create' : 'update',
        path,
        dataValue
      );

      modificationValidation.errors.forEach(error => {
        newErrors.push(error.message);
      });

      // 显示警告（不阻止保存）
      if (validation.warnings.length > 0) {
        validation.warnings.forEach(warning => {
          console.warn(`警告 [${warning.path.join('.')}]: ${warning.message}`);
        });
      }

    } catch (error) {
      newErrors.push('数据格式验证失败');
      console.error('验证错误:', error);
    }

    setErrors(newErrors);
    return newErrors.length === 0;
  };

  // 转换表单数据为DataValue
  const convertToDataValue = (): DataValue => {
    switch (valueType) {
      case 'null':
        return null;
      case 'boolean':
        return value === 'true';
      case 'number':
        return Number(value);
      case 'string':
        return value;
      case 'object':
        const obj: DataObject = {};
        Object.entries(objectData).forEach(([key, val]) => {
          // 尝试转换为合适的类型
          if (val === 'null') {
            obj[key] = null;
          } else if (val === 'true' || val === 'false') {
            obj[key] = val === 'true';
          } else if (!isNaN(Number(val)) && val !== '') {
            obj[key] = Number(val);
          } else {
            obj[key] = val;
          }
        });
        return obj;
      case 'rule':
        const rule: DataRule = {
          type: 'rule',
          ruleId: ruleData.ruleId,
          description: ruleData.description,
          conditions: ruleData.conditions.map(c => ({
            condition: c.condition,
            value: c.value === 'null' ? null : 
                   c.value === 'true' ? true :
                   c.value === 'false' ? false :
                   !isNaN(Number(c.value)) && c.value !== '' ? Number(c.value) : c.value,
            description: c.description
          }))
        };
        return rule;
      default:
        return value;
    }
  };

  // 处理保存
  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      setIsLoading(true);
      const dataValue = convertToDataValue();
      await onSave(path, dataValue);
      toast.success(mode === 'create' ? '创建成功' : '更新成功');
      onOpenChange(false);
    } catch (error) {
      console.error('保存失败:', error);
      toast.error('保存失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 处理删除
  const handleDelete = async () => {
    if (!onDelete) return;

    try {
      setIsLoading(true);
      await onDelete(path);
      toast.success('删除成功');
      onOpenChange(false);
    } catch (error) {
      console.error('删除失败:', error);
      toast.error('删除失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 添加对象属性
  const addObjectProperty = () => {
    const newKey = `key_${Object.keys(objectData).length + 1}`;
    setObjectData({ ...objectData, [newKey]: '' });
  };

  // 删除对象属性
  const removeObjectProperty = (key: string) => {
    const newData = { ...objectData };
    delete newData[key];
    setObjectData(newData);
  };

  // 添加规则条件
  const addRuleCondition = () => {
    setRuleData({
      ...ruleData,
      conditions: [...ruleData.conditions, { condition: '', value: '', description: '' }]
    });
  };

  // 删除规则条件
  const removeRuleCondition = (index: number) => {
    setRuleData({
      ...ruleData,
      conditions: ruleData.conditions.filter((_, i) => i !== index)
    });
  };

  const getTitle = () => {
    switch (mode) {
      case 'create': return '创建数据项';
      case 'edit': return '编辑数据项';
      case 'delete': return '删除数据项';
      default: return '数据编辑';
    }
  };

  const getDescription = () => {
    const pathStr = path.length > 0 ? path.join(' > ') : '根节点';
    switch (mode) {
      case 'create': return `在 ${pathStr} 下创建新的数据项`;
      case 'edit': return `编辑路径: ${pathStr}`;
      case 'delete': return `确认删除路径: ${pathStr}`;
      default: return '';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{getTitle()}</DialogTitle>
          <DialogDescription>{getDescription()}</DialogDescription>
        </DialogHeader>

        {errors.length > 0 && (
          <Alert variant="destructive">
            <IconAlertCircle className="h-4 w-4" />
            <AlertDescription>
              <ul className="list-disc list-inside">
                {errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        {mode === 'delete' ? (
          <div className="py-4">
            <p className="text-sm text-muted-foreground">
              此操作不可撤销，确认要删除这个数据项吗？
            </p>
            {currentValue !== undefined && (
              <div className="mt-4 p-3 bg-gray-50 rounded-md">
                <p className="text-sm font-medium">当前值:</p>
                <code className="text-sm">{JSON.stringify(currentValue, null, 2)}</code>
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {/* 数据类型选择 */}
            <div className="space-y-2">
              <Label>数据类型</Label>
              <Select value={valueType} onValueChange={(val: any) => setValueType(val)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="string">字符串</SelectItem>
                  <SelectItem value="number">数字</SelectItem>
                  <SelectItem value="boolean">布尔值</SelectItem>
                  <SelectItem value="null">空值</SelectItem>
                  <SelectItem value="object">对象</SelectItem>
                  <SelectItem value="rule">规则</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* 根据类型显示不同的编辑界面 */}
            {valueType === 'string' && (
              <div className="space-y-2">
                <Label>字符串值</Label>
                <Input
                  value={value}
                  onChange={(e) => setValue(e.target.value)}
                  placeholder="输入字符串值"
                />
              </div>
            )}

            {valueType === 'number' && (
              <div className="space-y-2">
                <Label>数字值</Label>
                <Input
                  type="number"
                  value={value}
                  onChange={(e) => setValue(e.target.value)}
                  placeholder="输入数字值"
                />
              </div>
            )}

            {valueType === 'boolean' && (
              <div className="space-y-2">
                <Label>布尔值</Label>
                <Select value={value} onValueChange={setValue}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="true">true</SelectItem>
                    <SelectItem value="false">false</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}

            {valueType === 'null' && (
              <div className="space-y-2">
                <Label>空值</Label>
                <Badge variant="outline">null</Badge>
              </div>
            )}

            {valueType === 'object' && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label>对象属性</Label>
                  <Button size="sm" variant="outline" onClick={addObjectProperty}>
                    <IconPlus className="h-4 w-4 mr-1" />
                    添加属性
                  </Button>
                </div>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {Object.entries(objectData).map(([key, val]) => (
                    <div key={key} className="flex gap-2">
                      <Input
                        placeholder="键"
                        value={key}
                        onChange={(e) => {
                          const newData = { ...objectData };
                          delete newData[key];
                          newData[e.target.value] = val;
                          setObjectData(newData);
                        }}
                        className="flex-1"
                      />
                      <Input
                        placeholder="值"
                        value={val}
                        onChange={(e) => setObjectData({ ...objectData, [key]: e.target.value })}
                        className="flex-1"
                      />
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => removeObjectProperty(key)}
                      >
                        <IconTrash className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {valueType === 'rule' && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>规则ID</Label>
                  <Input
                    value={ruleData.ruleId}
                    onChange={(e) => setRuleData({ ...ruleData, ruleId: e.target.value })}
                    placeholder="输入规则ID"
                  />
                </div>
                <div className="space-y-2">
                  <Label>规则描述</Label>
                  <Textarea
                    value={ruleData.description}
                    onChange={(e) => setRuleData({ ...ruleData, description: e.target.value })}
                    placeholder="输入规则描述"
                  />
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label>规则条件</Label>
                    <Button size="sm" variant="outline" onClick={addRuleCondition}>
                      <IconPlus className="h-4 w-4 mr-1" />
                      添加条件
                    </Button>
                  </div>
                  <div className="space-y-3 max-h-40 overflow-y-auto">
                    {ruleData.conditions.map((condition, index) => (
                      <div key={index} className="space-y-2 p-3 border rounded-md">
                        <div className="flex gap-2">
                          <Input
                            placeholder="条件表达式"
                            value={condition.condition}
                            onChange={(e) => {
                              const newConditions = [...ruleData.conditions];
                              newConditions[index].condition = e.target.value;
                              setRuleData({ ...ruleData, conditions: newConditions });
                            }}
                            className="flex-1"
                          />
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => removeRuleCondition(index)}
                          >
                            <IconTrash className="h-4 w-4" />
                          </Button>
                        </div>
                        <Input
                          placeholder="返回值"
                          value={condition.value}
                          onChange={(e) => {
                            const newConditions = [...ruleData.conditions];
                            newConditions[index].value = e.target.value;
                            setRuleData({ ...ruleData, conditions: newConditions });
                          }}
                        />
                        <Input
                          placeholder="条件描述（可选）"
                          value={condition.description}
                          onChange={(e) => {
                            const newConditions = [...ruleData.conditions];
                            newConditions[index].description = e.target.value;
                            setRuleData({ ...ruleData, conditions: newConditions });
                          }}
                        />
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          {mode === 'delete' ? (
            <Button variant="destructive" onClick={handleDelete} disabled={isLoading}>
              {isLoading ? '删除中...' : '确认删除'}
            </Button>
          ) : (
            <Button onClick={handleSave} disabled={isLoading}>
              {isLoading ? '保存中...' : '保存'}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
